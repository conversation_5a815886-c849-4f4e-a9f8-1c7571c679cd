<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';
require_once 'security_functions.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado e é administrador
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null || $_SESSION['cargo_utilizador'] != '1') {
    header("Location: Projeto.php?msg=Acesso restrito a administradores");
    exit;
}

// Inicializar variáveis
$problemas = [];
$correcoes = [];
$registros_restaurados = 0;

// Verificar se a tabela registro_horas existe
$query_check_table = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'registro_horas'";
$result_check_table = mysqli_query($conn, $query_check_table);
$row_check_table = mysqli_fetch_assoc($result_check_table);
$table_exists = ($row_check_table['count'] > 0);

if (!$table_exists) {
    $problemas[] = "A tabela 'registro_horas' não existe no banco de dados.";
    
    // Verificar se existe a tabela 'registos_horas' (versão em português)
    $query_check_alt = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'registos_horas'";
    $result_check_alt = mysqli_query($conn, $query_check_alt);
    $row_check_alt = mysqli_fetch_assoc($result_check_alt);
    
    if ($row_check_alt['count'] > 0) {
        $problemas[] = "Encontrada tabela alternativa 'registos_horas'.";
        $correcoes[] = "Executar script de correção para padronizar tabela de registro de horas.";
    }
} else {
    // Verificar estrutura da tabela
    $query_check_structure = "DESCRIBE registro_horas";
    $result_check_structure = mysqli_query($conn, $query_check_structure);
    
    $expected_columns = ['id', 'id_obra', 'id_usuario', 'horas', 'data_registro', 'descricao', 'criado_em'];
    $existing_columns = [];
    
    while ($row = mysqli_fetch_assoc($result_check_structure)) {
        $existing_columns[] = $row['Field'];
    }
    
    $missing_columns = array_diff($expected_columns, $existing_columns);
    
    if (!empty($missing_columns)) {
        $problemas[] = "Colunas ausentes na tabela 'registro_horas': " . implode(", ", $missing_columns);
        $correcoes[] = "Executar script de correção para adicionar colunas ausentes.";
    }
    
    // Verificar registros órfãos
    $query_check_orphans = "SELECT COUNT(*) as count FROM registro_horas WHERE id_obra NOT IN (SELECT obras_id FROM obras) OR id_usuario NOT IN (SELECT id_utilizadores FROM utilizadores)";
    $result_check_orphans = mysqli_query($conn, $query_check_orphans);
    $row_check_orphans = mysqli_fetch_assoc($result_check_orphans);
    
    if ($row_check_orphans['count'] > 0) {
        $problemas[] = "Encontrados {$row_check_orphans['count']} registros órfãos (referências a obras ou usuários inexistentes).";
        $correcoes[] = "Executar script de correção para remover registros órfãos.";
    }
    
    // Verificar foreign keys
    $query_check_fk = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'registro_horas' AND REFERENCED_TABLE_NAME IS NOT NULL";
    $result_check_fk = mysqli_query($conn, $query_check_fk);
    $row_check_fk = mysqli_fetch_assoc($result_check_fk);
    
    if ($row_check_fk['count'] < 2) {
        $problemas[] = "Foreign keys ausentes ou incompletas na tabela 'registro_horas'.";
        $correcoes[] = "Executar script de correção para recriar foreign keys.";
    }
}

// Se o botão de correção foi pressionado
if (isset($_POST['corrigir']) && $_POST['corrigir'] == 1) {
    // Executar script de correção
    $script_path = "../SQL/fix_registro_horas_table.sql";
    
    if (file_exists($script_path)) {
        // Ler o script SQL
        $sql_script = file_get_contents($script_path);
        
        // Dividir o script em comandos individuais
        $commands = explode(';', $sql_script);
        
        // Executar cada comando
        foreach ($commands as $command) {
            $command = trim($command);
            if (!empty($command)) {
                mysqli_query($conn, $command);
            }
        }
        
        $correcoes[] = "Script de correção executado com sucesso.";
        
        // Verificar se a tabela foi criada/corrigida
        $query_check_table = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'registro_horas'";
        $result_check_table = mysqli_query($conn, $query_check_table);
        $row_check_table = mysqli_fetch_assoc($result_check_table);
        $table_exists = ($row_check_table['count'] > 0);
        
        if ($table_exists) {
            $correcoes[] = "Tabela 'registro_horas' verificada e corrigida.";
        }
    } else {
        $problemas[] = "Script de correção não encontrado: $script_path";
    }
}

// Verificar se há registros na tabela
if ($table_exists) {
    $query_count = "SELECT COUNT(*) as count FROM registro_horas";
    $result_count = mysqli_query($conn, $query_count);
    $row_count = mysqli_fetch_assoc($result_count);
    $total_registros = $row_count['count'];
}

// HTML da página
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificar Registros - BUILT ORGANIZER</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="wrapper">
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <a href="Projeto.php">
                                <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                            </a>
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>
                        
                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php">OBRAS</a></li>
                        <?php endif; ?>
                        
                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                        <?php endif; ?>
                        
                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php">HORAS</a></li>
                        <?php endif; ?>
                        
                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>
                        
                        <?php if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] == '1')): ?>
                            <li class="visible-menu-item"><a href="Registar_utilizador.php" id="admin-menu">NOVO UTILIZADOR</a></li>
                        <?php endif; ?>
                        
                        <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Verificação de Registros de Horas</h5>
                        </div>
                        <div class="card-body">
                            <h6>Status da Tabela</h6>
                            
                            <?php if ($table_exists): ?>
                                <div class="alert alert-success">
                                    <i class="bi bi-check-circle-fill"></i> Tabela 'registro_horas' existe no banco de dados.
                                </div>
                                <p>Total de registros: <strong><?php echo $total_registros; ?></strong></p>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <i class="bi bi-exclamation-triangle-fill"></i> Tabela 'registro_horas' não encontrada!
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($problemas)): ?>
                                <h6 class="mt-4">Problemas Encontrados</h6>
                                <div class="alert alert-warning">
                                    <ul class="mb-0">
                                        <?php foreach ($problemas as $problema): ?>
                                            <li><?php echo $problema; ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($correcoes)): ?>
                                <h6 class="mt-4">Correções Aplicadas</h6>
                                <div class="alert alert-info">
                                    <ul class="mb-0">
                                        <?php foreach ($correcoes as $correcao): ?>
                                            <li><?php echo $correcao; ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                            
                            <form method="post" class="mt-4">
                                <input type="hidden" name="corrigir" value="1">
                                <button type="submit" class="btn btn-warning">
                                    <i class="bi bi-tools"></i> Executar Correções
                                </button>
                                <a href="Projeto pag 4.php" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i> Voltar
                                </a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>