-- =====================================================
-- SCRIPT PARA CORRIGIR E PADRONIZAR ESTRUTURA DAS TABELAS
-- =====================================================
-- Este script corrige inconsistências nas tabelas e foreign keys
-- para garantir que as ligações estejam corretas

USE controle_obras;

-- =====================================================
-- 1. VERIFICAR E CORRIGIR TABELA OBRAS
-- =====================================================

-- Verificar se a tabela obras existe com a estrutura correta
CREATE TABLE IF NOT EXISTS `obras` (
  `obras_id` INT NOT NULL AUTO_INCREMENT,
  `nome_obra` VARCHAR(255) NOT NULL,
  `localizacao` VARCHAR(255) NULL,
  `endereço` VARCHAR(255) NULL,
  `data_inicio` DATE NOT NULL,
  `data_fim_prevista` DATE NULL,
  `data_fim_real` DATE NULL,
  `data_fim` DATE NULL,
  `orcamento` DECIMAL(15, 2) NULL,
  `descricao` TEXT NULL,
  `status` VARCHAR(50) DEFAULT 'Em andamento',
  `id_responsavel` INT NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`obras_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_data_inicio` (`data_inicio`),
  INDEX `idx_responsavel` (`id_responsavel`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- =====================================================
-- 2. VERIFICAR E CORRIGIR TABELA UTILIZADORES
-- =====================================================

-- Verificar se a tabela utilizadores existe com a estrutura correta
CREATE TABLE IF NOT EXISTS `utilizadores` (
  `id_utilizadores` INT NOT NULL AUTO_INCREMENT,
  `nome_utilizador` VARCHAR(255) NOT NULL UNIQUE,
  `email_utilizador` VARCHAR(255) NOT NULL,
  `password_utilizador` VARCHAR(255) NOT NULL,
  `cargo_utilizador` INT NULL DEFAULT NULL,
  `data_criacao` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_utilizadores`),
  INDEX `idx_cargo` (`cargo_utilizador`),
  INDEX `idx_nome` (`nome_utilizador`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- =====================================================
-- 3. VERIFICAR E CORRIGIR TABELA CARGOS
-- =====================================================

CREATE TABLE IF NOT EXISTS `cargos` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `cargo` VARCHAR(100) NOT NULL,
  `nivel_acesso` INT NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Inserir cargos padrão se não existirem
INSERT IGNORE INTO `cargos` (`id`, `cargo`, `nivel_acesso`) VALUES
(1, 'Administrador', 1),
(2, 'Gerente', 2),
(3, 'Supervisor', 3),
(4, 'Funcionário', 4);

-- =====================================================
-- 4. VERIFICAR E CORRIGIR TABELA REGISTRO_HORAS
-- =====================================================

-- Criar tabela registro_horas com estrutura padronizada
CREATE TABLE IF NOT EXISTS `registro_horas` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `id_obra` INT NOT NULL,
  `id_usuario` INT NOT NULL,
  `horas` DECIMAL(5, 2) NOT NULL,
  `data_registro` DATE NOT NULL,
  `descricao` TEXT NULL,
  `criado_em` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_obra` (`id_obra`),
  INDEX `idx_usuario` (`id_usuario`),
  INDEX `idx_data` (`data_registro`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- =====================================================
-- 5. ADICIONAR/CORRIGIR FOREIGN KEYS
-- =====================================================

-- Remover foreign keys existentes se houver problemas
SET FOREIGN_KEY_CHECKS = 0;

-- Adicionar foreign key para obras -> utilizadores (responsável)
ALTER TABLE `obras` 
DROP FOREIGN KEY IF EXISTS `fk_obras_responsavel`,
ADD CONSTRAINT `fk_obras_responsavel` 
FOREIGN KEY (`id_responsavel`) 
REFERENCES `utilizadores` (`id_utilizadores`) 
ON DELETE SET NULL 
ON UPDATE CASCADE;

-- Adicionar foreign key para utilizadores -> cargos
ALTER TABLE `utilizadores` 
DROP FOREIGN KEY IF EXISTS `fk_utilizadores_cargos`,
ADD CONSTRAINT `fk_utilizadores_cargos` 
FOREIGN KEY (`cargo_utilizador`) 
REFERENCES `cargos` (`id`) 
ON DELETE SET NULL 
ON UPDATE CASCADE;

-- Adicionar foreign keys para registro_horas
ALTER TABLE `registro_horas` 
DROP FOREIGN KEY IF EXISTS `fk_registro_horas_obras`,
DROP FOREIGN KEY IF EXISTS `fk_registro_horas_usuarios`,
ADD CONSTRAINT `fk_registro_horas_obras` 
FOREIGN KEY (`id_obra`) 
REFERENCES `obras` (`obras_id`) 
ON DELETE CASCADE 
ON UPDATE CASCADE,
ADD CONSTRAINT `fk_registro_horas_usuarios` 
FOREIGN KEY (`id_usuario`) 
REFERENCES `utilizadores` (`id_utilizadores`) 
ON DELETE CASCADE 
ON UPDATE CASCADE;

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 6. VERIFICAR INTEGRIDADE DOS DADOS
-- =====================================================

-- Verificar se há registros órfãos em registro_horas
SELECT 'Verificando registros órfãos...' as status;

-- Registros de horas sem obra válida
SELECT COUNT(*) as registros_sem_obra_valida
FROM registro_horas rh
LEFT JOIN obras o ON rh.id_obra = o.obras_id
WHERE o.obras_id IS NULL;

-- Registros de horas sem usuário válido
SELECT COUNT(*) as registros_sem_usuario_valido
FROM registro_horas rh
LEFT JOIN utilizadores u ON rh.id_usuario = u.id_utilizadores
WHERE u.id_utilizadores IS NULL;

-- =====================================================
-- 7. CRIAR VIEWS PARA COMPATIBILIDADE
-- =====================================================

-- View para compatibilidade com código que usa nomes diferentes
CREATE OR REPLACE VIEW `registos_horas_view` AS
SELECT 
    `id`,
    `id` AS `id_registro`,
    `id_obra`,
    `id_usuario`,
    `id_usuario` AS `id_utilizador`,
    `horas`,
    `data_registro`,
    `data_registro` AS `data_registo`,
    `descricao`,
    `criado_em`,
    `criado_em` AS `timestamp`
FROM `registro_horas`;

-- =====================================================
-- 8. MENSAGEM DE CONCLUSÃO
-- =====================================================

SELECT 'Estrutura das tabelas corrigida e padronizada com sucesso!' as resultado;
