<?php
// Iniciar sessão se ainda não estiver iniciada
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Log para verificar se o arquivo está sendo carregado
error_log("verificar_permissao.php carregado");

// Função para verificar permissões de acesso
function checkMenuAccess($cargo, $menu) {
    // Debug para verificar os valores recebidos
    error_log("checkMenuAccess: cargo=$cargo, menu=$menu");

    // Log adicional para depuração
    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
    $caller = isset($backtrace[1]['file']) ? basename($backtrace[1]['file']) : 'unknown';
    $line = isset($backtrace[1]['line']) ? $backtrace[1]['line'] : 'unknown';
    error_log("checkMenuAccess chamado de: $caller:$line");

    // Definir permissões por cargo
    $permissoes = [
        '1' => ['OBRAS', 'ORCAMENTOS', 'HORAS', 'GESTAO', 'ADMINISTRACAO'], // Admin - acesso total
        '2' => ['OBRAS', 'ORCAMENTOS', 'HORAS', 'GESTAO'], // Gerente - agora tem acesso a GESTAO
        '3' => ['OBRAS', 'HORAS'], // Supervisor
        '4' => ['HORAS'], // Funcionário
    ];

    // Se o cargo não existir nas permissões, negar acesso
    if (!isset($permissoes[$cargo])) {
        error_log("Cargo não encontrado nas permissões: $cargo");
        return false;
    }

    // Verificar se o menu está nas permissões do cargo
    $tem_acesso = in_array($menu, $permissoes[$cargo]);
    error_log("Acesso ao menu $menu para cargo $cargo: " . ($tem_acesso ? "permitido" : "negado"));
    return $tem_acesso;
}

// Função para verificar se o usuário tem permissão para acessar a página atual
function verificarPermissaoPagina($paginaAtual) {
    // Se o usuário não estiver logado e não for a página inicial, redireciona para a página de login
    if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
        // Se for a página inicial, permite o acesso
        if ($paginaAtual == 'Projeto.php') {
            return true;
        }

        // Redireciona para a página inicial
        header("Location: ./Projeto.php");
        exit();
    }

    // Mapeia o nome da página para o menu correspondente
    $menuMap = [
        'Projeto pag 2.php' => 'OBRAS',
        'Projeto pag 3.php' => 'ORCAMENTOS',
        'Projeto pag 4.php' => 'HORAS',
        'Projeto pag 5.php' => 'GESTAO',
        'Registar_utilizador.php' => 'ADMINISTRACAO'
    ];

    // Páginas de administração que requerem nível de acesso 1
    $adminPages = [
        'Registar_utilizador.php',
        'Criar_utilizador.php'
    ];

    // Verificar se é uma página de administração
    if (in_array($paginaAtual, $adminPages)) {
        if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'ADMINISTRACAO')) {
            // Redireciona para a página inicial com mensagem de erro
            $_SESSION['AccessError'] = "Acesso negado. Apenas administradores podem acessar esta página.";
            header("Location: ./Projeto.php");
            exit();
        }
        return true;
    }

    // Se a página atual não estiver no mapa, permite o acesso
    if (!isset($menuMap[$paginaAtual])) {
        return true;
    }

    // Verifica se o usuário tem permissão para acessar o menu
    if (!checkMenuAccess($_SESSION['cargo_utilizador'], $menuMap[$paginaAtual])) {
        // Redireciona para a página inicial com mensagem de erro
        $_SESSION['AccessError'] = "Você não tem permissão para acessar esta página.";
        header("Location: ./Projeto.php");
        exit();
    }

    return true;
}

/**
 * Verifica se o usuário tem permissão para editar horas
 * @param string $cargo Cargo do usuário
 * @param int $id_usuario_registro ID do usuário que registrou as horas
 * @param int $id_usuario_atual ID do usuário atual
 * @return bool Retorna true se o usuário tem permissão, false caso contrário
 */
function checkEditHorasPermission($cargo, $id_usuario_registro, $id_usuario_atual) {
    // Administradores (1) e gerentes (2) podem editar qualquer registro
    if ($cargo == '1' || $cargo == '2') {
        return true;
    }
    
    // Outros usuários só podem editar seus próprios registros
    return ($id_usuario_registro == $id_usuario_atual);
}

// Verifica se este arquivo está sendo chamado diretamente ou incluido em outro arquivo
// Se for chamado diretamente, executa a verificação de permissão
if (basename($_SERVER['SCRIPT_FILENAME']) == basename(__FILE__)) {
    // Obtém o nome do arquivo atual
    $currentPage = basename($_SERVER['PHP_SELF']);

    // Verifica se o usuário tem permissão para acessar a página atual
    verificarPermissaoPagina($currentPage);
}
?>


