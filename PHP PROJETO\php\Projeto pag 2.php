<?php
session_start();
require_once 'conexao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Tentar incluir o arquivo de verificação de permissão
@include_once 'verificar_permissao.php';

// Definir a função checkMenuAccess como fallback se não estiver disponível
if (!function_exists('checkMenuAccess')) {
    function checkMenuAccess($cargo, $menu) {
        // Definir permissões por cargo
        $permissoes = [
            '1' => ['OBRAS', 'ORCAMENTOS', 'HORAS', 'GESTAO', 'ADMINISTRACAO'], // Admin - acesso total
            '2' => ['OBRAS', 'ORCAMENTOS', 'HORAS'], // Gerente
            '3' => ['OBRAS', 'HORAS'], // Supervisor
            '4' => ['HORAS'], // Funcionário
        ];

        // Se o cargo não existir nas permissões, negar acesso
        if (!isset($permissoes[$cargo])) {
            return false;
        }

        // Verificar se o menu está nas permissões do cargo
        return in_array($menu, $permissoes[$cargo]);
    }
}

// Log para depuração
error_log("Arquivos incluídos com sucesso em Projeto pag 2.php");

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Gerar token CSRF
$csrf_token = generateCSRFToken();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=Faça login para acessar o sistema");
    exit;
}

// Conectar ao banco de dados
$conn = connectToDatabase();

// Adicionar depuração para verificar a conexão
if (!$conn) {
    die("Erro na conexão com o banco de dados: " . mysqli_connect_error());
}

// Verificar a estrutura da tabela obras
$query = "DESCRIBE obras";
$result = mysqli_query($conn, $query);
$colunas = [];

while ($row = mysqli_fetch_assoc($result)) {
    $colunas[] = $row['Field'];
}

// Determinar o nome correto da coluna ID
$coluna_id = in_array('obras_id', $colunas) ? 'obras_id' :
             (in_array('id_obra', $colunas) ? 'id_obra' :
             (in_array('id', $colunas) ? 'id' : 'obras_id'));

// Determinar o nome correto da coluna nome
$coluna_nome = in_array('nome_obra', $colunas) ? 'nome_obra' :
               (in_array('nome', $colunas) ? 'nome' : 'nome_obra');

// Determinar o nome correto da coluna endereço
$coluna_endereco = in_array('endereço', $colunas) ? 'endereço' :
                  (in_array('endereco', $colunas) ? 'endereco' :
                  (in_array('localizacao', $colunas) ? 'localizacao' : 'endereço'));

// Determinar o nome correto da coluna data_inicio
$coluna_data_inicio = in_array('data_inicio', $colunas) ? 'data_inicio' :
                     (in_array('data_inicio_real', $colunas) ? 'data_inicio_real' : 'data_inicio');

// Determinar o nome correto da coluna data_fim
$coluna_data_fim = in_array('data_fim', $colunas) ? 'data_fim' :
                  (in_array('data_fim_prevista', $colunas) ? 'data_fim_prevista' :
                  (in_array('prazo', $colunas) ? 'prazo' : 'data_fim'));

// Buscar todas as obras com os nomes corretos das colunas
$query_obras = "SELECT * FROM obras ORDER BY $coluna_data_inicio DESC";
$result_obras = mysqli_query($conn, $query_obras);

if (!$result_obras) {
    die("Erro na consulta: " . mysqli_error($conn));
}

$num_obras = mysqli_num_rows($result_obras);

// Verificar se há obras no banco de dados
if ($num_obras == 0) {
    // Adicionar uma obra de teste para verificar se o problema é com a exibição ou com os dados
    $query_teste = "INSERT INTO obras ($coluna_nome, $coluna_endereco, $coluna_data_inicio, status)
                   VALUES ('Obra de Teste', 'Endereço de Teste', NOW(), 'Em andamento')";

    // Comentar a linha abaixo após o teste
    // mysqli_query($conn, $query_teste);
}

// Função para determinar a cor do status
function getStatusColor($status) {
    switch ($status) {
        case 'Em andamento':
            return 'primary';
        case 'Concluída':
            return 'success';
        case 'Pausada':
            return 'secondary';
        default:
            return 'info';
    }
}
?>

<!DOCTYPE html>
<html lang="pt-pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Obras - Built Organizer</title>
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">

    <!-- CSS da biblioteca Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- CSS personalizado -->
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
    <link rel="stylesheet" href="menu_style.css">
    <!-- <script src="modal_fix.js"></script> -->
</head>
<body>
    <div class="wrapper">
        <!-- Menu de Navegação -->
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <a href="Projeto.php">
                                <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                            </a>
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php" class="active">OBRAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')): ?>
                            <li><a href="Projeto pag 5.php">GESTÃO</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] == '1')): ?>
                            <li class="visible-menu-item"><a href="Registar_utilizador.php" id="admin-menu">NOVO UTILIZADOR</a></li>
                        <?php endif; ?>

                        <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content">
            <!-- Cabeçalho da página -->
            <div class="page-header">
                <div class="container">
                    <h1><i class="bi bi-building"></i> Gestão de Obras</h1>
                    <p>Gerencie todas as obras da sua empresa em um só lugar.</p>
                </div>
            </div>

            <!-- Mensagens de alerta -->
            <?php if(isset($_SESSION['mensagem'])): ?>
                <div class="alert alert-<?php echo $_SESSION['tipo_mensagem']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['mensagem']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                </div>
                <?php unset($_SESSION['mensagem']); unset($_SESSION['tipo_mensagem']); ?>
            <?php endif; ?>

            <div class="container">
                <!-- Botões de ação -->
                <div class="action-buttons">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#adicionarObraModal">
                        <i class="bi bi-plus-circle"></i> Adicionar Nova Obra
                    </button>
                    <!-- Botão de registrar material removido -->
                </div>

                <!-- Tabela de Obras -->
                <div class="obras-table">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Nome da Obra</th>
                                    <th scope="col">Localização</th>
                                    <th scope="col">Data de Início</th>
                                    <th scope="col">Prazo</th>
                                    <!-- Coluna de Orçamento removida -->
                                    <th scope="col">Status</th>
                                    <th scope="col">Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Adicionar depuração para verificar se o loop está sendo executado
                                echo "<!-- Iniciando loop de obras. Número de obras: $num_obras -->";

                                // Reiniciar o ponteiro do resultado para garantir que todas as linhas sejam lidas
                                if ($result_obras && $num_obras > 0) {
                                    mysqli_data_seek($result_obras, 0);
                                    $contador = 1;

                                    while($obra = mysqli_fetch_assoc($result_obras)) {
                                        // Adicionar depuração para verificar os dados de cada obra
                                        echo "<!-- Obra #$contador: " . json_encode($obra) . " -->";
                                ?>
                                        <tr>
                                            <td><?php echo $contador++; ?></td>
                                            <td><?php echo htmlspecialchars($obra[$coluna_nome] ?? 'Nome não definido'); ?></td>
                                            <td><?php echo htmlspecialchars($obra[$coluna_endereco] ?? 'Endereço não definido'); ?></td>
                                            <td>
                                                <?php if(isset($obra[$coluna_data_inicio]) && $obra[$coluna_data_inicio]): ?>
                                                    <?php echo date('d/m/Y', strtotime($obra[$coluna_data_inicio])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Não definido</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if(isset($obra[$coluna_data_fim]) && $obra[$coluna_data_fim]): ?>
                                                    <?php echo date('d/m/Y', strtotime($obra[$coluna_data_fim])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Não definido</span>
                                                <?php endif; ?>
                                            </td>
                                            <!-- Célula de Orçamento removida -->
                                            <td>
                                                <?php
                                                $statusClass = getStatusColor($obra['status'] ?? 'Em andamento');
                                                $statusText = htmlspecialchars($obra['status'] ?? 'Em andamento');
                                                echo "<span class='status-badge bg-{$statusClass}'>{$statusText}</span>";
                                                ?>
                                            </td>
                                            <td class="action-cell">
                                                <a href="detalhes_obra.php?id=<?php echo $obra[$coluna_id]; ?>" class="btn btn-info btn-sm text-white" data-bs-toggle="tooltip" title="Ver Detalhes">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-warning btn-sm" onclick="editarObra('<?php echo $obra[$coluna_id]; ?>', '<?php echo htmlspecialchars($obra[$coluna_nome] ?? ''); ?>', '<?php echo htmlspecialchars($obra[$coluna_endereco] ?? ''); ?>', '<?php echo isset($obra[$coluna_data_inicio]) ? date('Y-m-d', strtotime($obra[$coluna_data_inicio])) : ''; ?>', '<?php echo isset($obra[$coluna_data_fim]) && $obra[$coluna_data_fim] ? date('Y-m-d', strtotime($obra[$coluna_data_fim])) : ''; ?>', '<?php echo isset($obra['descricao']) ? htmlspecialchars($obra['descricao']) : ''; ?>', '<?php echo htmlspecialchars($obra['status'] ?? 'Em andamento'); ?>')">
                                                    <i class="bi bi-pencil"></i> Editar
                                                </button>
                                                <button type="button" class="btn btn-success btn-sm" title="Registrar Material"
                                                        onclick="selecionarObraParaMaterial('<?php echo $obra[$coluna_id]; ?>', '<?php echo htmlspecialchars($obra[$coluna_nome] ?? ''); ?>')">
                                                    <i class="bi bi-box-seam"></i>
                                                </button>
                                                <!-- Botão de agendar vistoria removido -->
                                            </td>
                                        </tr>
                                <?php
                                    }
                                } else {
                                ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-3">
                                            <i class="bi bi-exclamation-triangle me-2"></i> Nenhuma obra cadastrada. Clique em "Adicionar Nova Obra" para começar.
                                        </td>
                                    </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Modificar a tabela de materiais por obra -->
            <div class="container mt-5">
                <h2 class="mb-4">Materiais por Obra</h2>
                <div class="obras-table">
                    <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th scope="col">Obra</th>
                                <th scope="col">Material</th>
                                <th scope="col">Quantidade</th>
                                <th scope="col">Unidade</th>
                                <th scope="col">Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Consulta para obter materiais com suas respectivas obras
                            $conn = connectToDatabase();

                            // Verificar se a coluna unidade_medida existe
                            $query = "SHOW COLUMNS FROM materiais LIKE 'unidade_medida'";
                            $result = mysqli_query($conn, $query);
                            $unidade_medida_exists = mysqli_num_rows($result) > 0;

                            // Ajustar a consulta com base na existência da coluna
                            if ($unidade_medida_exists) {
                                $query = "SELECT m.material_id, m.nome, m.quantidade, m.unidade_medida, m.obras_id, o.nome_obra
                                          FROM materiais m
                                          JOIN obras o ON m.obras_id = o.obras_id
                                          ORDER BY o.nome_obra, m.nome";
                            } else {
                                $query = "SELECT m.material_id, m.nome, m.quantidade, m.obras_id, o.nome_obra
                                          FROM materiais m
                                          JOIN obras o ON m.obras_id = o.obras_id
                                          ORDER BY o.nome_obra, m.nome";
                            }

                            $result = mysqli_query($conn, $query);

                            if (mysqli_num_rows($result) > 0) {
                                while ($row = mysqli_fetch_assoc($result)) {
                                    echo "<tr>";
                                    echo "<td>" . htmlspecialchars($row['nome_obra']) . "</td>";
                                    echo "<td>" . htmlspecialchars($row['nome']) . "</td>";
                                    echo "<td>" . htmlspecialchars($row['quantidade']) . "</td>";

                                    // Exibir unidade de medida se existir, caso contrário exibir "unidades"
                                    if ($unidade_medida_exists) {
                                        echo "<td>" . htmlspecialchars($row['unidade_medida']) . "</td>";
                                    } else {
                                        echo "<td>unidades</td>";
                                    }

                                    // Ajustar a função de edição com base na existência da coluna
                                    if ($unidade_medida_exists) {
                                        echo "<td class='action-cell'>
                                                <button class='btn btn-warning btn-sm' onclick='editarMaterial(" . $row['material_id'] . ", \"" . htmlspecialchars($row['nome']) . "\", " . $row['quantidade'] . ", " . $row['obras_id'] . ", \"" . htmlspecialchars($row['unidade_medida']) . "\")'><i class='bi bi-pencil'></i> Editar</button>
                                                <button class='btn btn-danger btn-sm' onclick='excluirMaterial(" . $row['material_id'] . ")'><i class='bi bi-trash'></i></button>
                                              </td>";
                                    } else {
                                        echo "<td class='action-cell'>
                                                <button class='btn btn-warning btn-sm' onclick='editarMaterial(" . $row['material_id'] . ", \"" . htmlspecialchars($row['nome']) . "\", " . $row['quantidade'] . ", " . $row['obras_id'] . ", \"unidades\")'><i class='bi bi-pencil'></i> Editar</button>
                                                <button class='btn btn-danger btn-sm' onclick='excluirMaterial(" . $row['material_id'] . ")'><i class='bi bi-trash'></i></button>
                                              </td>";
                                    }

                                    echo "</tr>";
                                }
                            } else {
                                echo "<tr><td colspan='5' class='text-center py-3'><i class='bi bi-exclamation-triangle me-2'></i> Nenhum material registrado.</td></tr>";
                            }
                            mysqli_close($conn);
                            ?>
                        </tbody>
                    </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <footer class="bg-dark text-white text-center py-3 mt-5">
            <p class="mb-0">&copy; 2025 Built Organizer. Todos os direitos reservados.</p>
        </footer>
    </div>

    <!-- Modal para Adicionar Nova Obra -->
    <div class="modal fade" id="adicionarObraModal" tabindex="-1" aria-labelledby="adicionarObraModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="adicionarObraModalLabel">Adicionar Nova Obra</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="formAdicionarObra" method="POST" action="processar_obra.php">
                        <!-- Token CSRF para segurança -->
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="nome_obra" class="form-label">Nome da Obra *</label>
                                <input type="text" class="form-control" id="nome_obra" name="nome_obra" required>
                            </div>
                            <div class="col-md-6">
                                <label for="localizacao" class="form-label">Localização *</label>
                                <input type="text" class="form-control" id="localizacao" name="localizacao" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="data_inicio" class="form-label">Data de Início *</label>
                                <input type="date" class="form-control" id="data_inicio" name="data_inicio" required>
                            </div>
                            <div class="col-md-6">
                                <label for="data_fim" class="form-label">Prazo</label>
                                <input type="date" class="form-control" id="data_fim" name="data_fim">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <!-- Campo de orçamento removido -->
                            <div class="col-md-6">
                                <label for="status" class="form-label">Status *</label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="Em andamento" selected>Em andamento</option>
                                    <option value="Concluída">Concluída</option>
                                    <option value="Pausada">Pausada</option>
                                    <option value="Cancelada">Cancelada</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="descricao" class="form-label">Descrição</label>
                            <textarea class="form-control" id="descricao" name="descricao" rows="3"></textarea>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                            <button type="submit" class="btn btn-primary">Adicionar Obra</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Editar Obra -->
    <div id="editarObraModalCustom" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1050;">
        <div style="position: relative; width: 80%; max-width: 800px; margin: 50px auto; background-color: white; border-radius: 5px; padding: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.5);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; border-bottom: 1px solid #dee2e6; padding-bottom: 10px;">
                <h5 style="margin: 0; font-size: 1.25rem;">Editar Obra</h5>
                <button type="button" onclick="fecharModalCustom()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">&times;</button>
            </div>
            <div>
                <form id="formEditarObra" method="POST" action="editar_obra.php">
                    <input type="hidden" id="id_obra_edit" name="id_obra">
                    <!-- Adicionar o token CSRF aqui -->
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                    <div style="margin-bottom: 15px;">
                        <label for="nome_obra_edit" style="display: block; margin-bottom: 5px;">Nome da Obra:</label>
                        <input type="text" id="nome_obra_edit" name="nome_obra" class="form-control" required style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label for="localizacao_edit" style="display: block; margin-bottom: 5px;">Localização:</label>
                        <input type="text" id="localizacao_edit" name="localizacao" class="form-control" required style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                    </div>

                    <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                        <div style="flex: 1;">
                            <label for="data_inicio_edit" style="display: block; margin-bottom: 5px;">Data de Início:</label>
                            <input type="date" id="data_inicio_edit" name="data_inicio" class="form-control" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                        </div>
                        <div style="flex: 1;">
                            <label for="data_fim_edit" style="display: block; margin-bottom: 5px;">Prazo:</label>
                            <input type="date" id="data_fim_edit" name="data_fim" class="form-control" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                        </div>
                    </div>

                    <!-- Campo de orçamento removido -->

                    <div style="margin-bottom: 15px;">
                        <label for="descricao_edit" style="display: block; margin-bottom: 5px;">Descrição:</label>
                        <textarea id="descricao_edit" name="descricao" class="form-control" rows="3" style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;"></textarea>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label for="status_edit" style="display: block; margin-bottom: 5px;">Status:</label>
                        <select id="status_edit" name="status" class="form-control" required style="width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px;">
                            <option value="Em andamento">Em andamento</option>
                            <option value="Concluída">Concluída</option>
                            <option value="Pausada">Pausada</option>
                            <option value="Cancelada">Cancelada</option>
                        </select>
                    </div>

                    <div style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;">
                        <button type="button" onclick="fecharModalCustom()" style="padding: 8px 16px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">Cancelar</button>
                        <button type="submit" style="padding: 8px 16px; background-color: #0d6efd; color: white; border: none; border-radius: 4px; cursor: pointer;">Salvar Alterações</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal para Registrar Material -->
    <div class="modal fade" id="registrarMaterialModal" tabindex="-1" aria-labelledby="registrarMaterialModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="registrarMaterialModalLabel">Registrar Material</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="formRegistrarMaterial" method="POST" action="processar_material.php">
                        <!-- Adicionar token CSRF aqui -->
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        <input type="hidden" id="obra_id_material" name="obra_id_material" value="">

                        <div class="mb-3">
                            <label for="nome_obra_material" class="form-label">Obra</label>
                            <input type="text" class="form-control" id="nome_obra_material" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="nome_material" class="form-label">Nome do Material</label>
                            <input type="text" class="form-control" id="nome_material" name="nome_material" required>
                        </div>

                        <div class="mb-3">
                            <label for="quantidade" class="form-label">Quantidade</label>
                            <input type="number" class="form-control" id="quantidade" name="quantidade" min="1" required>
                        </div>

                        <div class="mb-3">
                            <label for="unidade_medida" class="form-label">Unidade de Medida</label>
                            <select class="form-select" id="unidade_medida" name="unidade_medida" required>
                                <option value="unidades">Unidades</option>
                                <option value="kg">Quilogramas (kg)</option>
                                <option value="m">Metros (m)</option>
                                <option value="m2">Metros Quadrados (m²)</option>
                                <option value="m3">Metros Cúbicos (m³)</option>
                                <option value="l">Litros (l)</option>
                                <option value="pacote">Pacotes</option>
                                <option value="caixa">Caixas</option>
                            </select>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                            <button type="submit" class="btn btn-primary">Registrar Material</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Editar Material -->
    <div class="modal fade" id="editarMaterialModal" tabindex="-1" aria-labelledby="editarMaterialModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editarMaterialModalLabel">Editar Material</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="formEditarMaterial" method="POST" action="editar_material.php">
                        <input type="hidden" id="material_id_edit" name="material_id" value="">
                        <!-- Adicionar token CSRF aqui -->
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                        <div class="mb-3">
                            <label for="nome_material_edit" class="form-label">Nome do Material *</label>
                            <input type="text" class="form-control" id="nome_material_edit" name="nome_material_edit" required>
                        </div>

                        <div class="mb-3">
                            <label for="quantidade_edit" class="form-label">Quantidade *</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="quantidade_edit" name="quantidade_edit" min="1" required>
                                <select class="form-select" id="unidade_medida_edit" name="unidade_medida_edit">
                                    <option value="unidades">Unidades</option>
                                    <option value="metros">Metros</option>
                                    <option value="kg">Kg</option>
                                    <option value="litros">Litros</option>
                                    <option value="m²">m²</option>
                                    <option value="m³">m³</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="obra_id_material_edit" class="form-label">Obra *</label>
                            <select class="form-select" id="obra_id_material_edit" name="obra_id_material_edit" required>
                                <?php
                                // Reiniciar o ponteiro do resultado para garantir que todas as linhas sejam lidas
                                if ($result_obras && $num_obras > 0) {
                                    mysqli_data_seek($result_obras, 0);
                                    while($obra = mysqli_fetch_assoc($result_obras)) {
                                        echo "<option value='" . $obra[$coluna_id] . "'>" . htmlspecialchars($obra[$coluna_nome]) . "</option>";
                                    }
                                }
                                ?>
                            </select>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                            <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript da biblioteca Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Script para corrigir o estilo do menu ADMINISTRAÇÃO -->
    <script src="fix_admin_menu.js"></script>
    <!-- Scripts para corrigir problemas com tokens CSRF -->
    <script src="csrf_fix.js"></script>
    <script src="csrf_init.js"></script>





    <!-- JavaScript personalizado -->
    <script>
        // Função para preencher o formulário de edição
        function editarObra(id, nome, endereco, dataInicio, dataFim, descricao, status) {
            // Preencher os campos do formulário
            document.getElementById('id_obra_edit').value = id;
            document.getElementById('nome_obra_edit').value = nome;
            document.getElementById('localizacao_edit').value = endereco;
            document.getElementById('data_inicio_edit').value = dataInicio;
            document.getElementById('data_fim_edit').value = dataFim;
            document.getElementById('descricao_edit').value = descricao || '';
            document.getElementById('status_edit').value = status;

            // Abrir o modal usando a API do Bootstrap
            var editarObraModal = new bootstrap.Modal(document.getElementById('editarObraModal'));
            editarObraModal.show();
        }

        // Função para selecionar obra para registro de material
        function selecionarObraParaMaterial(id, nome) {
            console.log('Selecionando obra para material:', id, nome);

            // Preencher os campos do formulário de material
            document.getElementById('obra_id_material').value = id;
            document.getElementById('nome_obra_material').value = nome;

            // Limpar outros campos do formulário
            document.getElementById('nome_material').value = '';
            document.getElementById('quantidade').value = '';
            document.getElementById('unidade_medida').value = 'unidades';

            // Abrir o modal usando nossa função personalizada
            openCustomModal('registrarMaterialModal');
        }
    </script>
    <script>
        // Função para editar material
        function editarMaterial(id, nome, quantidade, obraId, unidadeMedida) {
            // Preencher o formulário de edição
            document.getElementById('material_id_edit').value = id;
            document.getElementById('nome_material_edit').value = nome;
            document.getElementById('quantidade_edit').value = quantidade;
            document.getElementById('obra_id_material_edit').value = obraId;
            document.getElementById('unidade_medida_edit').value = unidadeMedida;

            // Garantir que o token CSRF esteja atualizado
            if (document.querySelector('#formEditarMaterial input[name="csrf_token"]')) {
                // Se o campo já existir, atualizamos seu valor
                document.querySelector('#formEditarMaterial input[name="csrf_token"]').value =
                    document.querySelector('input[name="csrf_token"]').value;
            }

            // Abrir o modal usando Bootstrap
            var modal = new bootstrap.Modal(document.getElementById('editarMaterialModal'));
            modal.show();
        }

        // Função para confirmar exclusão de material
        function excluirMaterial(id) {
            if (confirm('Tem certeza que deseja excluir este material?')) {
                window.location.href = 'excluir_material.php?id=' + id;
            }
        }
    </script>
    <script>
        // Função para selecionar obra para registro de material
        function selecionarObraParaMaterial(id, nome) {
            // Preencher os campos do formulário de material
            document.getElementById('obra_id_material').value = id;
            document.getElementById('nome_obra_material').value = nome;

            // Abrir o modal usando Bootstrap
            var modal = new bootstrap.Modal(document.getElementById('registrarMaterialModal'));
            modal.show();
        }
    </script>
    <script>
        // Função para fechar o modal corretamente
        function fecharModal(modalId) {
            const modalElement = document.getElementById(modalId);
            const modalInstance = bootstrap.Modal.getInstance(modalElement);

            if (modalInstance) {
                modalInstance.hide();
            } else {
                // Fallback caso a instância não seja encontrada
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
                modalElement.classList.remove('show');
                modalElement.style.display = 'none';
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }
        }

        // Adicionar event listeners para os botões de cancelar
        document.addEventListener('DOMContentLoaded', function() {
            // Botões de cancelar dentro dos modais
            const botoesFechar = document.querySelectorAll('[data-bs-dismiss="modal"]');

            botoesFechar.forEach(botao => {
                botao.addEventListener('click', function() {
                    const modalId = this.closest('.modal').id;
                    fecharModal(modalId);
                });
            });

            // Adicionar event listener para o botão de cancelar no modal de editar obra
            const btnCancelarEditarObra = document.querySelector('#editarObraModal .btn-secondary');
            if (btnCancelarEditarObra) {
                btnCancelarEditarObra.addEventListener('click', function() {
                    fecharModal('editarObraModal');
                });
            }
        });

        // Adicionar event listener para fechar modais com ESC ou clique fora
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modaisAbertos = document.querySelectorAll('.modal.show');
                modaisAbertos.forEach(modal => {
                    fecharModal(modal.id);
                });
            }
        });

        // Adicionar event listener para cliques no backdrop
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal') && event.target.classList.contains('show')) {
                fecharModal(event.target.id);
            }
        });

        // Garantir que o backdrop seja removido quando a página for carregada
        window.addEventListener('load', function() {
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop && !document.querySelector('.modal.show')) {
                backdrop.remove();
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }
        });
    </script>
    <!-- Botão de emergência fixo (sempre visível) -->
    <!--
    <div style="position: fixed; bottom: 20px; left: 20px; z-index: 9999;">
        <button class="btn btn-warning" onclick="forceCleanupModals()">
            <i class="bi bi-tools"></i> Reparar Modais
        </button>
    </div>
    -->
    <!-- Botão de emergência para limpar modais travados -->
    <div style="position: fixed; bottom: 20px; right: 20px; z-index: 9999;">
        <button class="btn btn-danger" onclick="limparModaisTravados()" style="display: none;" id="btnLimparModais">
            <i class="bi bi-x-circle"></i> Limpar Modais Travados
        </button>
    </div>

    <script>
        // Função para limpar modais travados
        function limparModaisTravados() {
            console.log("Limpando modais travados");

            // Remover todos os backdrops
            document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());

            // Fechar todos os modais
            document.querySelectorAll('.modal').forEach(modal => {
                modal.classList.remove('show');
                modal.style.display = 'none';
                modal.setAttribute('aria-hidden', 'true');
                modal.removeAttribute('aria-modal');
                modal.removeAttribute('role');
            });

            // Restaurar o body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // Esconder o botão
            document.getElementById('btnLimparModais').style.display = 'none';
        }

        // Detectar modais travados
        document.addEventListener('DOMContentLoaded', function() {
            setInterval(function() {
                const backdrop = document.querySelector('.modal-backdrop');
                const modalAberto = document.querySelector('.modal.show');

                if (backdrop && !modalAberto) {
                    // Modal travado detectado
                    document.getElementById('btnLimparModais').style.display = 'block';
                } else if (!backdrop) {
                    document.getElementById('btnLimparModais').style.display = 'none';
                }
            }, 2000);
        });
    </script>
    <!-- Executar limpeza de modais quando a página carregar -->
    <script>
        window.addEventListener('load', function() {
            // Pequeno atraso para garantir que o DOM esteja completamente carregado
            setTimeout(function() {
                cleanupOrphanedBackdrops();
            }, 500);
        });
    </script>
    <!-- Adicionar event listeners específicos para os modais de material -->
    <!--
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Botão de cancelar no modal de registrar material
            const btnCancelarRegistrarMaterial = document.querySelector('#registrarMaterialModal .btn-secondary');
            if (btnCancelarRegistrarMaterial) {
                btnCancelarRegistrarMaterial.addEventListener('click', function(e) {
                    e.preventDefault();
                    closeCustomModal('registrarMaterialModal');
                });
            }

            // Botão de cancelar no modal de editar material
            const btnCancelarEditarMaterial = document.querySelector('#editarMaterialModal .btn-secondary');
            if (btnCancelarEditarMaterial) {
                btnCancelarEditarMaterial.addEventListener('click', function(e) {
                    e.preventDefault();
                    closeCustomModal('editarMaterialModal');
                });
            }

            // Botões de fechar (X) nos modais de material
            const btnFecharRegistrarMaterial = document.querySelector('#registrarMaterialModal .btn-close');
            if (btnFecharRegistrarMaterial) {
                btnFecharRegistrarMaterial.addEventListener('click', function(e) {
                    e.preventDefault();
                    closeCustomModal('registrarMaterialModal');
                });
            }

            const btnFecharEditarMaterial = document.querySelector('#editarMaterialModal .btn-close');
            if (btnFecharEditarMaterial) {
                btnFecharEditarMaterial.addEventListener('click', function(e) {
                    e.preventDefault();
                    closeCustomModal('editarMaterialModal');
                });
            }
        });
    </script>
    -->
    <!-- Adicionar event listeners para os formulários de material -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Formulário de registrar material
            const formRegistrarMaterial = document.getElementById('formRegistrarMaterial');
            if (formRegistrarMaterial) {
                formRegistrarMaterial.addEventListener('submit', function() {
                    // Armazenar que o formulário foi enviado para evitar fechamento automático
                    sessionStorage.setItem('materialFormSubmitted', 'true');
                });
            }

            // Formulário de editar material
            const formEditarMaterial = document.getElementById('formEditarMaterial');
            if (formEditarMaterial) {
                formEditarMaterial.addEventListener('submit', function() {
                    // Armazenar que o formulário foi enviado para evitar fechamento automático
                    sessionStorage.setItem('materialFormSubmitted', 'true');
                });
            }

            // Verificar se há mensagem de sucesso após envio do formulário
            if (document.querySelector('.alert-success')) {
                // Se o formulário foi enviado anteriormente, limpar a flag
                if (sessionStorage.getItem('materialFormSubmitted') === 'true') {
                    sessionStorage.removeItem('materialFormSubmitted');
                    // Forçar limpeza de modais
                    forceCleanupModals();
                }
            }
        });
    </script>
    <!-- Remover o script específico para o formulário de registro de materiais -->
    <!--
    <script>
        // Script específico para o formulário de registro de materiais
        document.addEventListener('DOMContentLoaded', function() {
            const formRegistrarMaterial = document.getElementById('formRegistrarMaterial');
            if (formRegistrarMaterial) {
                formRegistrarMaterial.addEventListener('submit', function(e) {
                    // ... validação ...
                });
            }
        });
    </script>
    -->
    <!-- Script específico para o modal de edição de obras -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Botão de cancelar no modal de editar obra
            const btnCancelarEditarObra = document.querySelector('#editarObraModal .btn-secondary');
            if (btnCancelarEditarObra) {
                btnCancelarEditarObra.addEventListener('click', function() {
                    // Fechar o modal usando a API do Bootstrap
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editarObraModal'));
                    if (modal) {
                        modal.hide();
                    }
                });
            }

            // Botão de fechar (X) no modal de editar obra
            const btnFecharEditarObra = document.querySelector('#editarObraModal .btn-close');
            if (btnFecharEditarObra) {
                btnFecharEditarObra.addEventListener('click', function() {
                    // Fechar o modal usando a API do Bootstrap
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editarObraModal'));
                    if (modal) {
                        modal.hide();
                    }
                });
            }
        });
    </script>
    <script>
        // Verificar se o Bootstrap está carregado corretamente
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar se o Bootstrap está disponível
            if (typeof bootstrap === 'undefined') {
                console.error('Bootstrap não está carregado. Verifique se o arquivo bootstrap.bundle.min.js está sendo incluído corretamente.');
            } else {
                console.log('Bootstrap carregado com sucesso.');

                // Inicializar todos os modais
                document.querySelectorAll('.modal').forEach(function(modalEl) {
                    new bootstrap.Modal(modalEl);
                });
            }
        });
    </script>
    <script>
        // Função para forçar o fechamento do modal de edição de obras
        function forcarFecharModalEditarObra() {
            console.log("Forçando fechamento do modal de edição de obras");

            // 1. Tentar usar a API do Bootstrap
            try {
                const modalElement = document.getElementById('editarObraModal');
                const modalInstance = bootstrap.Modal.getInstance(modalElement);

                if (modalInstance) {
                    modalInstance.hide();
                    console.log("Modal fechado via API Bootstrap");
                } else {
                    throw new Error("Instância do modal não encontrada");
                }
            } catch (error) {
                console.error("Erro ao fechar modal via API Bootstrap:", error);

                // 2. Método alternativo: manipulação direta do DOM
                try {
                    // Remover classes e atributos do modal
                    const modal = document.getElementById('editarObraModal');
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                    modal.setAttribute('aria-hidden', 'true');
                    modal.removeAttribute('aria-modal');
                    modal.removeAttribute('role');

                    // Remover backdrop
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }

                    // Restaurar o body
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';

                    console.log("Modal fechado via manipulação DOM");
                } catch (domError) {
                    console.error("Erro ao fechar modal via DOM:", domError);
                }
            }
        }

        // Adicionar event listeners para os botões no modal de edição de obras
        document.addEventListener('DOMContentLoaded', function() {
            // Botão Cancelar
            const btnCancelar = document.querySelector('#editarObraModal .btn-secondary');
            if (btnCancelar) {
                btnCancelar.addEventListener('click', function(e) {
                    e.preventDefault();
                    forcarFecharModalEditarObra();
                });
            }

            // Botão X (fechar)
            const btnFechar = document.querySelector('#editarObraModal .btn-close');
            if (btnFechar) {
                btnFechar.addEventListener('click', function(e) {
                    e.preventDefault();
                    forcarFecharModalEditarObra();
                });
            }

            // Clicar fora do modal
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('modal') && e.target.id === 'editarObraModal') {
                    forcarFecharModalEditarObra();
                }
            });

            // Tecla ESC
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && document.querySelector('#editarObraModal.show')) {
                    forcarFecharModalEditarObra();
                }
            });
        });
    </script>
    <script>
        // Funções para o modal personalizado
        function abrirModalCustom() {
            document.getElementById('editarObraModalCustom').style.display = 'block';
            document.body.style.overflow = 'hidden'; // Impedir rolagem do body
        }

        function fecharModalCustom() {
            document.getElementById('editarObraModalCustom').style.display = 'none';
            document.body.style.overflow = ''; // Restaurar rolagem do body
        }

        // Função para editar obra usando o modal personalizado
        function editarObra(id, nome, endereco, dataInicio, dataFim, descricao, status) {
            // Preencher os campos do formulário
            document.getElementById('id_obra_edit').value = id;
            document.getElementById('nome_obra_edit').value = nome;
            document.getElementById('localizacao_edit').value = endereco;
            document.getElementById('data_inicio_edit').value = dataInicio;
            document.getElementById('data_fim_edit').value = dataFim;
            document.getElementById('descricao_edit').value = descricao || '';
            document.getElementById('status_edit').value = status;

            // Abrir o modal personalizado
            abrirModalCustom();
        }

        // Adicionar event listeners para o modal personalizado
        document.addEventListener('DOMContentLoaded', function() {
            // Fechar o modal ao clicar fora dele
            document.getElementById('editarObraModalCustom').addEventListener('click', function(e) {
                if (e.target === this) {
                    fecharModalCustom();
                }
            });

            // Fechar o modal com a tecla ESC
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && document.getElementById('editarObraModalCustom').style.display === 'block') {
                    fecharModalCustom();
                }
            });
        });
    </script>
    <script>
        // Desativar o modal original do Bootstrap
        document.addEventListener('DOMContentLoaded', function() {
            // Remover o modal original do DOM para evitar conflitos
            const modalOriginal = document.getElementById('editarObraModal');
            if (modalOriginal) {
                modalOriginal.remove();
            }
        });
    </script>
    <script>
        // Função para fechar modais corretamente
        function closeCustomModal(modalId) {
            try {
                // 1. Tentar usar a API do Bootstrap
                const modalElement = document.getElementById(modalId);
                const modalInstance = bootstrap.Modal.getInstance(modalElement);

                if (modalInstance) {
                    modalInstance.hide();
                    console.log(`Modal ${modalId} fechado via API Bootstrap`);
                    return;
                }

                // 2. Se não houver instância, criar uma e depois fechar
                if (!modalInstance && modalElement) {
                    const tempModal = new bootstrap.Modal(modalElement);
                    tempModal.hide();
                    console.log(`Modal ${modalId} fechado via nova instância Bootstrap`);
                    return;
                }

                // 3. Método alternativo: manipulação direta do DOM
                if (modalElement) {
                    modalElement.classList.remove('show');
                    modalElement.style.display = 'none';
                    modalElement.setAttribute('aria-hidden', 'true');
                    modalElement.removeAttribute('aria-modal');
                    modalElement.removeAttribute('role');

                    // Remover backdrop
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }

                    // Restaurar o body
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';

                    console.log(`Modal ${modalId} fechado via manipulação DOM`);
                }
            } catch (error) {
                console.error(`Erro ao fechar modal ${modalId}:`, error);

                // 4. Último recurso: recarregar a página
                // location.reload();
            }
        }

        // Adicionar event listeners para todos os modais quando o DOM estiver carregado
        document.addEventListener('DOMContentLoaded', function() {
            // Configurar todos os botões de fechar e cancelar nos modais
            const modais = [
                'adicionarObraModal',
                'editarObraModal',
                'registrarMaterialModal',
                'editarMaterialModal'
            ];

            modais.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (!modal) return;

                // Botão X (fechar)
                const btnFechar = modal.querySelector('.btn-close');
                if (btnFechar) {
                    btnFechar.addEventListener('click', function(e) {
                        e.preventDefault();
                        closeCustomModal(modalId);
                    });
                }

                // Botão Cancelar
                const btnCancelar = modal.querySelector('.btn-secondary');
                if (btnCancelar) {
                    btnCancelar.addEventListener('click', function(e) {
                        e.preventDefault();
                        closeCustomModal(modalId);
                    });
                }

                // Clicar fora do modal
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeCustomModal(modalId);
                    }
                });
            });

            // Tecla ESC para fechar qualquer modal aberto
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const modalAberto = document.querySelector('.modal.show');
                    if (modalAberto) {
                        closeCustomModal(modalAberto.id);
                    }
                }
            });
        });
    </script>
    <script>
        // Verificar o token CSRF antes de enviar o formulário
        document.addEventListener('DOMContentLoaded', function() {
            const formEditarObra = document.getElementById('formEditarObra');
            if (formEditarObra) {
                formEditarObra.addEventListener('submit', function(e) {
                    const csrfToken = this.querySelector('input[name="csrf_token"]');
                    if (!csrfToken || !csrfToken.value) {
                        e.preventDefault();
                        console.error('Token CSRF não encontrado ou vazio!');
                        alert('Erro de segurança: Token CSRF não encontrado. A página será recarregada.');
                        location.reload();
                        return false;
                    }
                    console.log('Formulário sendo enviado com token CSRF: ' + csrfToken.value.substring(0, 10) + '...');
                });
            }
        });
    </script>

</body>
</html>
