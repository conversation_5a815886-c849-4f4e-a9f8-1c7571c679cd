<?php
session_start();
require_once 'conexao.php';

// Verificar se o usuário está logado e é administrador
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    die("Usuário não está logado. Faça login primeiro.");
}

if (!isset($_SESSION['cargo_utilizador']) || $_SESSION['cargo_utilizador'] != '1') {
    die("Apenas administradores podem executar este script.");
}

echo "<h2>Criar Registros de Teste para Operários</h2>";
echo "<hr>";

// Conectar ao banco de dados
$conn = connectToDatabase();

// Verificar se a tabela registro_horas existe
$table_exists = false;
$result_check = mysqli_query($conn, "SHOW TABLES LIKE 'registro_horas'");
if (mysqli_num_rows($result_check) > 0) {
    $table_exists = true;
    echo "<p style='color: green;'>✓ Tabela 'registro_horas' existe</p>";
} else {
    echo "<p style='color: red;'>✗ Tabela 'registro_horas' NÃO existe</p>";
    die("Não é possível continuar sem a tabela.");
}

// Buscar operários (cargo 4) e supervisores (cargo 3)
$query_operarios = "SELECT id_utilizadores, nome_utilizador, cargo_utilizador FROM utilizadores WHERE cargo_utilizador IN ('3', '4')";
$result_operarios = mysqli_query($conn, $query_operarios);

if (!$result_operarios || mysqli_num_rows($result_operarios) == 0) {
    echo "<p style='color: orange;'>Nenhum operário ou supervisor encontrado.</p>";
    die("Não há operários para criar registros de teste.");
}

echo "<h3>Operários/Supervisores encontrados:</h3>";
echo "<ul>";
while ($operario = mysqli_fetch_assoc($result_operarios)) {
    echo "<li>ID: {$operario['id_utilizadores']} - Nome: {$operario['nome_utilizador']} - Cargo: {$operario['cargo_utilizador']}</li>";
}
echo "</ul>";

// Reset do ponteiro do resultado
mysqli_data_seek($result_operarios, 0);

// Buscar obras disponíveis
$query_obras = "SELECT obras_id, nome_obra FROM obras LIMIT 5";
$result_obras = mysqli_query($conn, $query_obras);

if (!$result_obras || mysqli_num_rows($result_obras) == 0) {
    echo "<p style='color: orange;'>Nenhuma obra encontrada. Criando uma obra de teste...</p>";
    
    // Criar uma obra de teste
    $query_create_obra = "INSERT INTO obras (nome_obra, descricao_obra, data_inicio, status_obra) 
                          VALUES ('Obra de Teste', 'Obra criada para testes de registro de horas', CURDATE(), 'Em andamento')";
    if (mysqli_query($conn, $query_create_obra)) {
        echo "<p style='color: green;'>✓ Obra de teste criada com sucesso!</p>";
        $result_obras = mysqli_query($conn, $query_obras);
    } else {
        echo "<p style='color: red;'>✗ Erro ao criar obra de teste: " . mysqli_error($conn) . "</p>";
        die("Não é possível continuar sem obras.");
    }
}

echo "<h3>Obras disponíveis:</h3>";
echo "<ul>";
$obras_ids = [];
while ($obra = mysqli_fetch_assoc($result_obras)) {
    echo "<li>ID: {$obra['obras_id']} - Nome: {$obra['nome_obra']}</li>";
    $obras_ids[] = $obra['obras_id'];
}
echo "</ul>";

// Reset do ponteiro do resultado
mysqli_data_seek($result_operarios, 0);

// Criar registros de teste
echo "<h3>Criando registros de teste...</h3>";

$registros_criados = 0;
$erros = 0;

while ($operario = mysqli_fetch_assoc($result_operarios)) {
    $user_id = $operario['id_utilizadores'];
    $nome_usuario = $operario['nome_utilizador'];
    
    // Verificar se já existem registros para este usuário
    $query_check = "SELECT COUNT(*) as total FROM registro_horas WHERE id_usuario = $user_id";
    $result_check = mysqli_query($conn, $query_check);
    $existing_records = $result_check ? mysqli_fetch_assoc($result_check)['total'] : 0;
    
    if ($existing_records > 0) {
        echo "<p>Usuário $nome_usuario já possui $existing_records registros. Pulando...</p>";
        continue;
    }
    
    // Criar 3 registros de teste para cada operário
    for ($i = 0; $i < 3; $i++) {
        $obra_id = $obras_ids[array_rand($obras_ids)]; // Obra aleatória
        $horas = rand(4, 8) + (rand(0, 1) * 0.5); // Entre 4 e 8.5 horas
        $data_registro = date('Y-m-d', strtotime("-$i days")); // Últimos 3 dias
        $descricao = "Trabalho de teste " . ($i + 1) . " para " . $nome_usuario;
        
        $query_insert = "INSERT INTO registro_horas (id_obra, id_usuario, horas, data_registro, descricao) 
                         VALUES ($obra_id, $user_id, $horas, '$data_registro', '$descricao')";
        
        if (mysqli_query($conn, $query_insert)) {
            $registros_criados++;
            echo "<p style='color: green;'>✓ Registro criado para $nome_usuario: $horas horas em $data_registro</p>";
        } else {
            $erros++;
            echo "<p style='color: red;'>✗ Erro ao criar registro para $nome_usuario: " . mysqli_error($conn) . "</p>";
        }
    }
}

echo "<hr>";
echo "<h3>Resumo:</h3>";
echo "<p><strong>Registros criados:</strong> $registros_criados</p>";
echo "<p><strong>Erros:</strong> $erros</p>";

if ($registros_criados > 0) {
    echo "<p style='color: green;'>✓ Registros de teste criados com sucesso!</p>";
    echo "<p><a href='Projeto pag 4.php'>← Voltar para a página de horas</a></p>";
} else {
    echo "<p style='color: orange;'>Nenhum registro foi criado.</p>";
}

mysqli_close($conn);
?>
