<?php
// Iniciar sessão
session_start();

// Incluir arquivos necessários
require_once 'conexao.php';
require_once 'dashboard_obras.php';
require_once 'verificar_permissao.php';
require_once 'security_functions.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=Faça login para acessar o sistema");
    exit;
}

// Verificar permissão para acessar a página de gestão
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'GESTAO')) {
    header("Location: Projeto.php?msg=Você não tem permissão para acessar esta página");
    exit;
}

// Obter conexão com o banco de dados
$conn = connectToDatabase(); // Alterado de getConnection() para connectToDatabase()

// Verificar se a conexão foi estabelecida
if (!$conn) {
    die("Erro na conexão com o banco de dados");
}

// DEBUG: Mostrar estrutura das tabelas
if (isset($_GET['debug'])) {
    echo "<h2>Estrutura das Tabelas</h2>";
    $tables = ['obras', 'utilizadores', 'registro_horas'];

    foreach ($tables as $table) {
        echo "<h3>Tabela: $table</h3>";
        $result = mysqli_query($conn, "SHOW COLUMNS FROM $table");

        if ($result) {
            echo "<table border='1'><tr><th>Campo</th><th>Tipo</th></tr>";
            while ($row = mysqli_fetch_assoc($result)) {
                echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td></tr>";
            }
            echo "</table>";

            // Mostrar alguns dados
            $data_result = mysqli_query($conn, "SELECT * FROM $table LIMIT 3");
            if ($data_result && mysqli_num_rows($data_result) > 0) {
                echo "<h4>Amostra de Dados:</h4>";
                echo "<table border='1'><tr>";
                $fields = mysqli_fetch_fields($data_result);
                foreach ($fields as $field) {
                    echo "<th>{$field->name}</th>";
                }
                echo "</tr>";

                while ($data_row = mysqli_fetch_assoc($data_result)) {
                    echo "<tr>";
                    foreach ($data_row as $value) {
                        echo "<td>" . htmlspecialchars($value) . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>Sem dados ou erro: " . mysqli_error($conn) . "</p>";
            }
        } else {
            echo "<p>Erro ao verificar estrutura: " . mysqli_error($conn) . "</p>";
        }
        echo "<hr>";
    }
    exit;
}

// Obter dados do dashboard
$dashboardData = getDashboardData($conn);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão - Built Organizer</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="Imagem1.png" type="image/x-icon">

    <!-- CSS da biblioteca Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <!-- Ícones da biblioteca Bootstrap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

    <!-- CSS personalizado -->
    <link rel="stylesheet" href="projeto.css">
    <link rel="stylesheet" href="homepage.css">
    <link rel="stylesheet" href="menu_style.css">
    <style>
        /* Estilos personalizados para as barras de progresso */
        .obras-status-container .progress {
            background-color: #e0e0e0;
            height: 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            overflow: hidden;
        }

        .obras-status-container .progress-bar {
            background-color: #28A745;
            height: 100%;
            color: white;
        }

        /* Remover qualquer estilo que possa estar interferindo */
        .obras-status-container .progress-bar.bg-primary,
        .obras-status-container .progress-bar.bg-success,
        .obras-status-container .progress-bar.bg-info,
        .obras-status-container .progress-bar.bg-warning,
        .obras-status-container .progress-bar.bg-danger {
            background-color: #28A745 !important;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Menu de Navegação -->
        <div class="Menu">
            <nav>
                <div class="nav-container">
                    <ul>
                        <li class="logo-item">
                            <a href="Projeto.php">
                                <img src="Imagem1.png" alt="BUILT ORGANIZER" class="logotipo">
                            </a>
                        </li>
                        <li><a href="Projeto.php">INÍCIO</a></li>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'OBRAS')): ?>
                            <li><a href="Projeto pag 2.php">OBRAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'ORCAMENTOS')): ?>
                            <li><a href="Projeto pag 3.php">ORÇAMENTOS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')): ?>
                            <li><a href="Projeto pag 4.php">HORAS</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] == '1' || $_SESSION['cargo_utilizador'] == '2')): ?>
                            <li><a href="Projeto pag 5.php" class="active">GESTÃO</a></li>
                        <?php endif; ?>

                        <?php if(isset($_SESSION['cargo_utilizador']) && ($_SESSION['cargo_utilizador'] == '1')): ?>
                            <li class="visible-menu-item"><a href="Registar_utilizador.php" id="admin-menu">NOVO UTILIZADOR</a></li>
                        <?php endif; ?>

                        <li><a href="Projeto.php?action=logout">LOGOUT</a></li>
                    </ul>
                </div>
            </nav>
        </div>

        <div class="content">
            <!-- Mensagens de alerta -->
            <?php if(isset($_SESSION['mensagem'])): ?>
                <div class="alert alert-<?php echo $_SESSION['tipo_mensagem']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['mensagem']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                </div>
                <?php unset($_SESSION['mensagem']); unset($_SESSION['tipo_mensagem']); ?>
            <?php endif; ?>

            <!-- Cabeçalho da página -->
            <div class="page-header">
                <div class="container">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1><i class="bi bi-graph-up"></i> Dashboard de Gestão</h1>
                        <a href="Projeto pag 5.php" class="btn btn-outline-primary refresh-btn" title="Atualizar Dashboard">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                    </div>
                    <p>Visualize e gerencie os principais indicadores da sua empresa.</p>
                </div>
            </div>

            <div class="container">
                <!-- Cards de Resumo -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary mb-3">
                            <div class="card-body">
                                <h5 class="card-title"><i class="bi bi-building"></i> Obras Ativas</h5>
                                <p class="card-text display-4"><?php echo $dashboardData['obras_ativas']; ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-success mb-3">
                            <div class="card-body">
                                <h5 class="card-title"><i class="bi bi-check-circle"></i> Obras Concluídas</h5>
                                <p class="card-text display-4"><?php echo $dashboardData['obras_concluidas']; ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-info mb-3">
                            <div class="card-body">
                                <h5 class="card-title"><i class="bi bi-people"></i> Funcionários</h5>
                                <p class="card-text display-4"><?php echo $dashboardData['funcionarios']; ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-warning mb-3">
                            <div class="card-body">
                                <h5 class="card-title"><i class="bi bi-cash"></i> Orçamento Total</h5>
                                <p class="card-text display-4">
                                    <?php
                                        // Garantir que o valor seja um número
                                        $orcamento_total = isset($dashboardData['orcamento_total']) ? (float)$dashboardData['orcamento_total'] : 0;
                                        echo number_format($orcamento_total, 2, ',', '.');
                                    ?> €
                                </p>
                                <small class="text-white">
                                    <?php if(isset($dashboardData['orcamento_total']) && $dashboardData['orcamento_total'] > 0): ?>
                                        Inclui orçamentos aprovados
                                    <?php else: ?>
                                        Nenhum orçamento registrado
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gráficos e Tabelas -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="bi bi-bar-chart"></i> Status das Obras</h5>
                            </div>
                            <div class="card-body">
                                <div class="obras-status-container" style="max-height: 400px; overflow-y: auto; padding-right: 5px;">
                                    <?php if(isset($dashboardData['obras_progresso']) && count($dashboardData['obras_progresso']) > 0): ?>
                                        <?php foreach ($dashboardData['obras_progresso'] as $obra): ?>
                                            <?php
                                            // Definir progresso como 100% para obras concluídas
                                            $progresso = isset($obra['status']) && $obra['status'] == 'Concluída' ? 100 : (isset($obra['progresso']) ? $obra['progresso'] : 0);
                                            ?>
                                            <div class="mb-3">
                                                <div class="d-flex justify-content-between mb-1">
                                                    <span><?php echo htmlspecialchars($obra['nome_obra'] ?? 'Sem nome'); ?></span>
                                                    <span><?php echo $progresso; ?>%</span>
                                                </div>
                                                <div class="progress">
                                                    <div class="progress-bar"
                                                         role="progressbar"
                                                         style="width: <?php echo $progresso; ?>%;"
                                                         aria-valuenow="<?php echo $progresso; ?>"
                                                         aria-valuemin="0"
                                                         aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <small class="text-muted"><?php echo isset($obra['status']) ? $obra['status'] : 'Desconhecido'; ?></small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <p class="text-center py-3">
                                            <i class="bi bi-exclamation-triangle me-2"></i> Nenhuma obra cadastrada.
                                        </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tabela de Registros de Horas -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="bi bi-clock-history"></i> Registros de Horas</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Obra</th>
                                                <th>Funcionário</th>
                                                <th>Data</th>
                                                <th>Horas</th>
                                                <th>Descrição</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if(isset($dashboardData['registros_horas']) && count($dashboardData['registros_horas']) > 0): ?>
                                                <?php foreach($dashboardData['registros_horas'] as $registro): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($registro['nome_obra'] ?? 'Sem nome'); ?></td>
                                                        <td><?php echo htmlspecialchars($registro['nome_utilizador'] ?? 'Sem nome'); ?></td>
                                                        <td><?php echo isset($registro['data_registro']) ? date('d/m/Y', strtotime($registro['data_registro'])) : 'Sem data'; ?></td>
                                                        <td><span class="badge bg-primary"><?php echo number_format((float)($registro['horas'] ?? 0), 1); ?> h</span></td>
                                                        <td><?php echo htmlspecialchars($registro['descricao'] ?? 'Sem descrição'); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="5" class="text-center py-3">
                                                        <i class="bi bi-exclamation-triangle me-2"></i> Nenhum registro de horas encontrado.
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rodapé -->
        <footer class="bg-dark text-white text-center py-3 mt-5">
            <p class="mb-0">&copy; 2025 Built Organizer. Todos os direitos reservados.</p>
        </footer>
    </div>

    <!-- JavaScript da biblioteca Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Script para corrigir o estilo do menu ADMINISTRAÇÃO -->
    <script src="fix_admin_menu.js"></script>




    <!-- Scripts JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Código para atualização de próximos prazos removido

            // Outras funcionalidades do dashboard podem ser adicionadas aqui

            // Exemplo: atualizar o dashboard completo a cada 10 minutos
            function atualizarDashboard() {
                location.reload();
            }

            // Atualizar a cada 10 minutos (600000 ms)
            setInterval(atualizarDashboard, 600000);
        });
    </script>
</body>

<?php
// Agora podemos fechar a conexão
mysqli_close($conn);
?>

