<?php
/**
 * Script para verificar a integridade das ligações às tabelas da base de dados
 * Verifica se todas as tabelas existem, se as colunas estão corretas e se as foreign keys funcionam
 */

// Incluir configuração da base de dados
require_once 'config.php';

// Função para verificar se uma tabela existe
function tabelaExiste($conn, $nome_tabela) {
    $query = "SHOW TABLES LIKE '$nome_tabela'";
    $result = mysqli_query($conn, $query);
    return mysqli_num_rows($result) > 0;
}

// Função para obter estrutura de uma tabela
function obterEstrutura($conn, $nome_tabela) {
    $query = "DESCRIBE $nome_tabela";
    $result = mysqli_query($conn, $query);
    $estrutura = [];
    
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $estrutura[] = $row;
        }
    }
    
    return $estrutura;
}

// Função para verificar foreign keys
function verificarForeignKeys($conn, $nome_tabela) {
    $query = "SELECT 
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
              FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
              WHERE TABLE_SCHEMA = DATABASE() 
              AND TABLE_NAME = '$nome_tabela' 
              AND REFERENCED_TABLE_NAME IS NOT NULL";
    
    $result = mysqli_query($conn, $query);
    $foreign_keys = [];
    
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $foreign_keys[] = $row;
        }
    }
    
    return $foreign_keys;
}

// Função para testar uma query
function testarQuery($conn, $query, $descricao) {
    $result = mysqli_query($conn, $query);
    
    if ($result) {
        $num_rows = mysqli_num_rows($result);
        return [
            'sucesso' => true,
            'linhas' => $num_rows,
            'erro' => null
        ];
    } else {
        return [
            'sucesso' => false,
            'linhas' => 0,
            'erro' => mysqli_error($conn)
        ];
    }
}

?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação de Ligações BD - Built Organizer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        .status-ok { color: #28a745; }
        .status-erro { color: #dc3545; }
        .status-aviso { color: #ffc107; }
        .code-block { 
            background-color: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace; 
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="bi bi-database-check"></i> Verificação de Ligações à Base de Dados</h1>
        <p class="text-muted">Este relatório verifica a integridade das tabelas e ligações da base de dados.</p>
        
        <hr>
        
        <?php
        // Verificar conexão
        if (!$conn) {
            echo '<div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> 
                    <strong>Erro de Conexão:</strong> ' . mysqli_connect_error() . '
                  </div>';
            exit;
        }
        
        echo '<div class="alert alert-success">
                <i class="bi bi-check-circle"></i> 
                <strong>Conexão OK:</strong> Conectado à base de dados com sucesso.
              </div>';
        
        // Lista de tabelas esperadas
        $tabelas_esperadas = [
            'utilizadores' => [
                'colunas' => ['id_utilizadores', 'nome_utilizador', 'email_utilizador', 'password_utilizador', 'cargo_utilizador'],
                'pk' => 'id_utilizadores'
            ],
            'obras' => [
                'colunas' => ['obras_id', 'nome_obra', 'data_inicio', 'orcamento'],
                'pk' => 'obras_id'
            ],
            'registro_horas' => [
                'colunas' => ['id', 'id_obra', 'id_usuario', 'horas', 'data_registro', 'descricao'],
                'pk' => 'id'
            ],
            'cargos' => [
                'colunas' => ['id', 'cargo', 'nivel_acesso'],
                'pk' => 'id'
            ]
        ];
        ?>
        
        <!-- Verificação de Tabelas -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5><i class="bi bi-table"></i> Verificação de Tabelas</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Tabela</th>
                                <th>Status</th>
                                <th>Colunas Principais</th>
                                <th>Observações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tabelas_esperadas as $nome_tabela => $config): ?>
                                <tr>
                                    <td><strong><?php echo $nome_tabela; ?></strong></td>
                                    <td>
                                        <?php if (tabelaExiste($conn, $nome_tabela)): ?>
                                            <span class="status-ok"><i class="bi bi-check-circle"></i> Existe</span>
                                        <?php else: ?>
                                            <span class="status-erro"><i class="bi bi-x-circle"></i> Não encontrada</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php 
                                        if (tabelaExiste($conn, $nome_tabela)) {
                                            $estrutura = obterEstrutura($conn, $nome_tabela);
                                            $colunas_existentes = array_column($estrutura, 'Field');
                                            
                                            foreach ($config['colunas'] as $coluna) {
                                                if (in_array($coluna, $colunas_existentes)) {
                                                    echo '<span class="status-ok">' . $coluna . '</span> ';
                                                } else {
                                                    echo '<span class="status-erro">' . $coluna . '</span> ';
                                                }
                                            }
                                        } else {
                                            echo '<span class="text-muted">N/A</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php 
                                        if (tabelaExiste($conn, $nome_tabela)) {
                                            $foreign_keys = verificarForeignKeys($conn, $nome_tabela);
                                            if (!empty($foreign_keys)) {
                                                echo '<small>FK: ';
                                                foreach ($foreign_keys as $fk) {
                                                    echo $fk['COLUMN_NAME'] . '→' . $fk['REFERENCED_TABLE_NAME'] . ' ';
                                                }
                                                echo '</small>';
                                            }
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Teste de Queries Críticas -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5><i class="bi bi-search"></i> Teste de Queries Críticas</h5>
            </div>
            <div class="card-body">
                <?php
                $queries_teste = [
                    'Listar todas as obras' => "SELECT obras_id, nome_obra FROM obras LIMIT 5",
                    'Listar todos os utilizadores' => "SELECT id_utilizadores, nome_utilizador FROM utilizadores LIMIT 5",
                    'JOIN obras com registros de horas' => "SELECT o.nome_obra, COUNT(rh.id) as total_registros 
                                                           FROM obras o 
                                                           LEFT JOIN registro_horas rh ON o.obras_id = rh.id_obra 
                                                           GROUP BY o.obras_id LIMIT 5",
                    'JOIN utilizadores com registros de horas' => "SELECT u.nome_utilizador, COUNT(rh.id) as total_horas 
                                                                   FROM utilizadores u 
                                                                   LEFT JOIN registro_horas rh ON u.id_utilizadores = rh.id_usuario 
                                                                   GROUP BY u.id_utilizadores LIMIT 5",
                    'Query principal da página de horas (Admin)' => "SELECT rh.id as registro_id, rh.id_obra, rh.id_usuario, 
                                                                     rh.horas, rh.data_registro, rh.descricao, 
                                                                     o.nome_obra, u.nome_utilizador
                                                                     FROM registro_horas rh
                                                                     JOIN obras o ON rh.id_obra = o.obras_id
                                                                     JOIN utilizadores u ON rh.id_usuario = u.id_utilizadores
                                                                     ORDER BY rh.data_registro DESC LIMIT 3"
                ];
                
                foreach ($queries_teste as $descricao => $query) {
                    $resultado = testarQuery($conn, $query, $descricao);
                    
                    echo '<div class="mb-3">';
                    echo '<h6>' . $descricao . '</h6>';
                    
                    if ($resultado['sucesso']) {
                        echo '<div class="alert alert-success py-2">';
                        echo '<i class="bi bi-check-circle"></i> ';
                        echo '<strong>Sucesso:</strong> ' . $resultado['linhas'] . ' linha(s) retornada(s)';
                        echo '</div>';
                    } else {
                        echo '<div class="alert alert-danger py-2">';
                        echo '<i class="bi bi-x-circle"></i> ';
                        echo '<strong>Erro:</strong> ' . $resultado['erro'];
                        echo '</div>';
                    }
                    
                    echo '<div class="code-block">' . htmlspecialchars($query) . '</div>';
                    echo '</div>';
                }
                ?>
            </div>
        </div>
        
        <!-- Recomendações -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5><i class="bi bi-lightbulb"></i> Recomendações</h5>
            </div>
            <div class="card-body">
                <ul>
                    <li><strong>Se alguma tabela não existe:</strong> Execute o script SQL de criação das tabelas.</li>
                    <li><strong>Se alguma coluna está em falta:</strong> Execute o script de correção da estrutura.</li>
                    <li><strong>Se as queries falham:</strong> Verifique se as foreign keys estão configuradas corretamente.</li>
                    <li><strong>Para corrigir problemas:</strong> Execute o arquivo <code>corrigir_estrutura_tabelas.sql</code></li>
                </ul>
                
                <div class="mt-3">
                    <a href="corrigir_estrutura_tabelas.sql" class="btn btn-warning" download>
                        <i class="bi bi-download"></i> Baixar Script de Correção
                    </a>
                </div>
            </div>
        </div>
        
        <div class="text-center">
            <a href="Projeto pag 4.php" class="btn btn-primary">
                <i class="bi bi-arrow-left"></i> Voltar à Gestão de Horas
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php mysqli_close($conn); ?>
