<?php
session_start();
require_once 'conexao.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    die("Usuário não está logado. Faça login primeiro.");
}

echo "<h2>Debug - Acesso às Horas do Operário</h2>";
echo "<hr>";

// Conectar ao banco de dados
$conn = connectToDatabase();

// Informações da sessão
echo "<h3>Informações da Sessão:</h3>";
echo "<p><strong>Usuário:</strong> " . ($_SESSION['userName'] ?? 'não definido') . "</p>";
echo "<p><strong>ID do Usuário:</strong> " . ($_SESSION['user_id'] ?? 'não definido') . "</p>";
echo "<p><strong>Cargo:</strong> " . ($_SESSION['cargo_utilizador'] ?? 'não definido') . "</p>";
echo "<hr>";

// Verificar se a tabela registro_horas existe
echo "<h3>Verificação da Tabela:</h3>";
$table_exists = false;
$result_check = mysqli_query($conn, "SHOW TABLES LIKE 'registro_horas'");
if (mysqli_num_rows($result_check) > 0) {
    $table_exists = true;
    echo "<p style='color: green;'>✓ Tabela 'registro_horas' existe</p>";
} else {
    echo "<p style='color: red;'>✗ Tabela 'registro_horas' NÃO existe</p>";
}

if ($table_exists) {
    // Verificar estrutura da tabela
    echo "<h4>Estrutura da Tabela:</h4>";
    $result_structure = mysqli_query($conn, "DESCRIBE registro_horas");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th><th>Extra</th></tr>";
    while ($row = mysqli_fetch_assoc($result_structure)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar total de registros
    echo "<h4>Contagem de Registros:</h4>";
    $query_total = "SELECT COUNT(*) as total FROM registro_horas";
    $result_total = mysqli_query($conn, $query_total);
    if ($result_total) {
        $total_registros = mysqli_fetch_assoc($result_total)['total'];
        echo "<p><strong>Total de registros na tabela:</strong> $total_registros</p>";
    }
    
    // Verificar registros por usuário
    $user_id = $_SESSION['user_id'] ?? 0;
    $query_user = "SELECT COUNT(*) as total FROM registro_horas WHERE id_usuario = $user_id";
    $result_user = mysqli_query($conn, $query_user);
    if ($result_user) {
        $registros_usuario = mysqli_fetch_assoc($result_user)['total'];
        echo "<p><strong>Registros para o usuário ID $user_id:</strong> $registros_usuario</p>";
    }
    
    // Mostrar todos os registros da tabela (limitado a 10)
    echo "<h4>Últimos 10 Registros na Tabela:</h4>";
    $query_all = "SELECT rh.*, u.nome_utilizador FROM registro_horas rh 
                  LEFT JOIN utilizadores u ON rh.id_usuario = u.id_utilizadores 
                  ORDER BY rh.id DESC LIMIT 10";
    $result_all = mysqli_query($conn, $query_all);
    
    if ($result_all && mysqli_num_rows($result_all) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>ID Obra</th><th>ID Usuário</th><th>Nome Usuário</th><th>Horas</th><th>Data</th><th>Descrição</th></tr>";
        while ($row = mysqli_fetch_assoc($result_all)) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['id_obra'] . "</td>";
            echo "<td>" . $row['id_usuario'] . "</td>";
            echo "<td>" . ($row['nome_utilizador'] ?? 'N/A') . "</td>";
            echo "<td>" . $row['horas'] . "</td>";
            echo "<td>" . $row['data_registro'] . "</td>";
            echo "<td>" . htmlspecialchars($row['descricao'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>Nenhum registro encontrado na tabela.</p>";
    }
    
    // Verificar se o usuário atual existe na tabela utilizadores
    echo "<h4>Verificação do Usuário:</h4>";
    $query_check_user = "SELECT * FROM utilizadores WHERE id_utilizadores = $user_id";
    $result_check_user = mysqli_query($conn, $query_check_user);
    if ($result_check_user && mysqli_num_rows($result_check_user) > 0) {
        $user_data = mysqli_fetch_assoc($result_check_user);
        echo "<p style='color: green;'>✓ Usuário encontrado na tabela utilizadores:</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $user_data['id_utilizadores'] . "</li>";
        echo "<li><strong>Nome:</strong> " . $user_data['nome_utilizador'] . "</li>";
        echo "<li><strong>Cargo:</strong> " . $user_data['cargo_utilizador'] . "</li>";
        echo "<li><strong>Email:</strong> " . $user_data['email_utilizador'] . "</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ Usuário NÃO encontrado na tabela utilizadores!</p>";
    }
    
    // Testar a consulta específica para operários
    echo "<h4>Teste da Consulta para Operários:</h4>";
    $cargo = $_SESSION['cargo_utilizador'] ?? '0';
    if ($cargo == '3' || $cargo == '4') {
        $query_operario = "SELECT 
                            rh.id as registro_id, 
                            rh.id_obra, 
                            rh.id_usuario, 
                            rh.horas, 
                            rh.data_registro, 
                            rh.descricao, 
                            o.nome_obra, 
                            u.nome_utilizador
                        FROM 
                            registro_horas rh
                        JOIN 
                            obras o ON rh.id_obra = o.obras_id
                        JOIN 
                            utilizadores u ON rh.id_usuario = u.id_utilizadores
                        WHERE 
                            rh.id_usuario = $user_id
                        ORDER BY 
                            rh.data_registro DESC";
        
        echo "<p><strong>Consulta SQL:</strong></p>";
        echo "<pre>" . htmlspecialchars($query_operario) . "</pre>";
        
        $result_operario = mysqli_query($conn, $query_operario);
        if (!$result_operario) {
            echo "<p style='color: red;'>✗ Erro na consulta: " . mysqli_error($conn) . "</p>";
        } else {
            $num_registros = mysqli_num_rows($result_operario);
            echo "<p><strong>Registros encontrados:</strong> $num_registros</p>";
            
            if ($num_registros > 0) {
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>ID</th><th>Obra</th><th>Funcionário</th><th>Data</th><th>Horas</th><th>Descrição</th></tr>";
                while ($row = mysqli_fetch_assoc($result_operario)) {
                    echo "<tr>";
                    echo "<td>" . $row['registro_id'] . "</td>";
                    echo "<td>" . htmlspecialchars($row['nome_obra']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['nome_utilizador']) . "</td>";
                    echo "<td>" . $row['data_registro'] . "</td>";
                    echo "<td>" . $row['horas'] . "</td>";
                    echo "<td>" . htmlspecialchars($row['descricao'] ?? '') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    } else {
        echo "<p>Usuário não é operário ou supervisor (cargo: $cargo)</p>";
    }
}

echo "<hr>";
echo "<p><a href='Projeto pag 4.php'>← Voltar para a página de horas</a></p>";

mysqli_close($conn);
?>
